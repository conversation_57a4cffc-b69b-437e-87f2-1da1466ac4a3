# ============================================================================
# JoystickController.py - 手柄控制器 (整理优化版)
# 功能: 手柄输入处理、轨迹规划、ZMQ通信
# ============================================================================

# 导入模块
import pygame
import os
import zmq
import json
import time
import logging
import struct
import math
import warnings
import numpy as np
from collections import deque

# 环境配置
warnings.filterwarnings("ignore", category=UserWarning, module="pygame.pkgdata")
os.environ['SDL_VIDEODRIVER'] = 'dummy'
os.environ['SDL_AUDIODRIVER'] = 'dummy'
os.environ['PYGAME_HIDE_SUPPORT_PROMPT'] = '1'

# ============================================================================
# 配置常量
# ============================================================================
# 控制参数
xScale = 50
yScale = 50
zScale = 10
rzScale = 1
deadZoneThreshold = 0.007
XdeadZoneThreshold = 0.007
TIME_INTERVAL = 0.015  # 1/30=0.030

# 起点位置定义 (绝对坐标)
START_POSE = {
    'x': 0.0, 'y': 0.0, 'z': 0.0,
    'rx': 0.0, 'ry': 0.0, 'rz': 0.0
}

# ZMQ配置
ZMQ_IPC_PATH = "ipc:///tmp/joystick_control"
ZMQ_TOPIC = b"joystick_data"

# ============================================================================
# 工具函数
# ============================================================================
def accurate_delay_s(sleep_s, start_stamp, delay_s):
    """精确延时函数"""
    time.sleep(sleep_s)
    while time.perf_counter() - start_stamp < delay_s:
        pass

# ============================================================================
# 轨迹规划器类
# ============================================================================
class TrajectoryPlanner:
    """S曲线轨迹规划器"""
    
    def __init__(self):
        # 轨迹规划参数
        self.max_velocity = np.array([0.5, 0.5, 0.3, 1.0, 1.0, 1.0])  # m/s, rad/s
        self.max_acceleration = np.array([2.0, 2.0, 1.5, 3.0, 3.0, 3.0])  # m/s², rad/s²
        self.max_jerk = np.array([10.0, 10.0, 8.0, 15.0, 15.0, 15.0])  # m/s³, rad/s³
        
        # 状态变量
        self.current_velocity = np.zeros(6)
        self.current_acceleration = np.zeros(6)
        self.dt = TIME_INTERVAL
        self.velocity_history = deque(maxlen=5)
        
    def plan_velocity_trajectory(self, target_vel_array):
        """S曲线速度轨迹规划"""
        target_vel = np.array(target_vel_array)
        target_vel = np.clip(target_vel, -self.max_velocity, self.max_velocity)
        planned_velocity = self.current_velocity.copy()
        
        for i in range(6):
            vel_diff = target_vel[i] - self.current_velocity[i]
            max_accel_step = self.max_acceleration[i] * self.dt
            max_jerk_step = self.max_jerk[i] * self.dt * self.dt
            
            if abs(vel_diff) <= max_accel_step:
                planned_velocity[i] = target_vel[i]
                self.current_acceleration[i] = vel_diff / self.dt
            else:
                accel_step = max_accel_step if vel_diff > 0 else -max_accel_step
                accel_diff = accel_step - self.current_acceleration[i] * self.dt
                if abs(accel_diff) > max_jerk_step:
                    accel_step = self.current_acceleration[i] * self.dt + \
                               (max_jerk_step if accel_diff > 0 else -max_jerk_step)
                planned_velocity[i] = self.current_velocity[i] + accel_step
                self.current_acceleration[i] = accel_step / self.dt
        
        self.current_velocity = planned_velocity.copy()
        self.velocity_history.append(planned_velocity.copy())
        
        if len(self.velocity_history) > 1:
            return np.mean(self.velocity_history, axis=0)
        return planned_velocity
    
    def emergency_stop(self):
        """紧急停止"""
        self.current_velocity.fill(0)
        self.current_acceleration.fill(0)
        self.velocity_history.clear()
        return self.current_velocity
    
    def reset(self):
        """重置轨迹规划器状态"""
        self.current_velocity.fill(0)
        self.current_acceleration.fill(0)
        self.velocity_history.clear()

# ============================================================================
# ZMQ发布器类
# ============================================================================
class ZmqPublisher:
    """ZMQ发布器"""
    
    def __init__(self, ipc_path=ZMQ_IPC_PATH):
        self.ipc_path = ipc_path
        self.context = None
        self.socket = None
        self.connected = False

    def connect(self):
        """连接ZMQ"""
        connect_start = time.perf_counter()
        try:
            self.context = zmq.Context()
            self.socket = self.context.socket(zmq.PUB)
            self.socket.bind(self.ipc_path)
            self.connected = True
            
            total_time = (time.perf_counter() - connect_start) * 1000
            print(f"[ZMQ] Publisher connected successfully ({total_time:.1f} ms)")
            return True
        except Exception as e:
            total_time = (time.perf_counter() - connect_start) * 1000
            print(f"[ZMQ] Connect error after {total_time:.1f} ms: {e}")
            self.connected = False
            return False

    def send(self, data):
        """发送数据"""
        if not self.connected and not self.connect():
            return False
        try:
            self.socket.send_multipart([ZMQ_TOPIC, data])
            return True
        except Exception as e:
            print(f"ZMQ Send error: {e}")
            self.connected = False
            return False

    def close(self):
        """关闭连接"""
        self.connected = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        if self.context:
            try:
                self.context.term()
            except:
                pass
            self.context = None

# ============================================================================
# 手柄控制器类 (整合版)
# ============================================================================
class JoystickController:
    """手柄控制器 - 整合轨迹规划功能"""
    
    def __init__(self):
        self.joystick = None
        self.initialized = False
        self.current_pose = START_POSE.copy()
        
        # 摇杆平滑参数
        self.deadzone_threshold = deadZoneThreshold
        self.deadzone_x_threshold = XdeadZoneThreshold
        
        # 轨迹规划器
        self.trajectory_planner = TrajectoryPlanner()
        self.is_emergency_stop = False
        
        print(f"Initial pose: {self.current_pose}")

    def get_smoothed_axis_data(self, axis_data):
        """获取经过死区处理和非线性平滑的轴数据"""
        if not axis_data:
            return axis_data

        smoothed_data = []
        for i, value in enumerate(axis_data):
            # 选择死区阈值：X轴(轴4)使用专用阈值
            deadzone = self.deadzone_x_threshold if i == 4 else self.deadzone_threshold

            # 死区处理
            if abs(value) <= deadzone:
                smoothed_data.append(0.0)
                continue

            # 归一化并应用 Smoothstep 平滑
            sign = 1.0 if value > 0 else -1.0
            u = (abs(value) - deadzone) / (1.0 - deadzone)
            u = max(0.0, min(u, 1.0))

            # Smoothstep 基础映射: f(u) = 3u^2 - 2u^3
            smoothed = 3 * (u ** 2) - 2 * (u ** 3)
            smoothed_data.append(sign * smoothed)

        return smoothed_data

    def calculate_target_velocity(self, axis_data, hat_data):
        """计算目标速度"""
        hat = hat_data[0] if hat_data else (0, 0)
        
        # 死区阈值处理
        newAxis = [0 if abs(element) <= self.deadzone_threshold else element for element in axis_data]
        newAxis[4] = 0 if abs(axis_data[4]) <= self.deadzone_x_threshold else axis_data[4]
        
        # 计算目标速度 (m/s, rad/s)
        target_vx = (newAxis[1] * xScale - hat[1] * 0.5) / 1000.0 #(newAxis[4] * xScale - hat[1] * 0.5) / 1000.0
        target_vy = (newAxis[0] * yScale + hat[0] * 0.5) / 1000.0
        target_vz = (-(newAxis[2] + 1) + (newAxis[5] + 1)) / 2.0 * zScale / 1000.0
        target_vrx = 0.0
        target_vry = 0.0
        target_vrz = newAxis[3] * rzScale if len(newAxis) > 3 else 0.0
        
        return [target_vx, target_vy, target_vz, target_vrx, target_vry, target_vrz]

    def update_pose_with_planned_velocity(self, planned_velocity, reset_pressed=False):
        """使用规划后的速度更新位置"""
        if reset_pressed:
            # 重置到起点
            self.current_pose = START_POSE.copy()
            self.trajectory_planner.reset()
            return
        
        # 使用规划后的速度积分更新位置
        self.current_pose['x'] += planned_velocity[0] * TIME_INTERVAL
        self.current_pose['y'] += planned_velocity[1] * TIME_INTERVAL
        self.current_pose['z'] += planned_velocity[2] * TIME_INTERVAL
        self.current_pose['rx'] += planned_velocity[3] * TIME_INTERVAL
        self.current_pose['ry'] += planned_velocity[4] * TIME_INTERVAL
        self.current_pose['rz'] += planned_velocity[5] * TIME_INTERVAL

    def fast_init(self):
        """快速初始化方法"""
        init_start_time = time.perf_counter()
        try:
            print("[INIT] Attempting fast initialization...")
            os.environ['SDL_JOYSTICK_ALLOW_BACKGROUND_EVENTS'] = '1'
            pygame.display.set_mode((1, 1), pygame.NOFRAME)
            pygame.joystick.init()

            step_duration = (time.perf_counter() - init_start_time) * 1000
            print(f"[INIT] Fast initialization completed ({step_duration:.1f} ms)")
            return self._complete_joystick_setup(init_start_time)
        except Exception as e:
            print(f"[INIT] Fast initialization failed: {e}")
            return self.init()

    def init(self):
        """标准初始化pygame和手柄"""
        init_start_time = time.perf_counter()
        try:
            print("[INIT] Attempting optimized pygame initialization...")
            pygame.joystick.init()
            step_duration = (time.perf_counter() - init_start_time) * 1000
            print(f"[INIT] pygame.joystick.init() completed ({step_duration:.1f} ms)")
        except Exception as e:
            print(f"[INIT] Direct joystick init failed: {e}, falling back to full init...")
            pygame.init()
            pygame.joystick.init()
        return self._complete_joystick_setup(init_start_time)

    def _complete_joystick_setup(self, init_start_time):
        """完成手柄设置的通用方法"""
        print("[INIT] 正在搜索手柄...")
        search_start_time = time.perf_counter()

        joystick_count = pygame.joystick.get_count()
        wait_count = 0

        while joystick_count == 0:
            wait_count += 1
            if wait_count == 1:
                print("[INIT] 未检测到手柄，等待连接中...")
            elif wait_count % 10 == 0:
                elapsed = (time.perf_counter() - search_start_time) * 1000
                print(f"[INIT] 仍在等待手柄连接... (已等待 {elapsed:.0f} ms)")

            pygame.event.pump()
            time.sleep(0.1)
            pygame.joystick.quit()
            pygame.joystick.init()
            joystick_count = pygame.joystick.get_count()

        search_duration = (time.perf_counter() - search_start_time) * 1000
        print(f"[INIT] 检测到 {joystick_count} 个手柄 (搜索耗时: {search_duration:.1f} ms)")

        try:
            self.joystick = pygame.joystick.Joystick(0)
            self.joystick.init()
            self.initialized = True

            joystick_name = self.joystick.get_name()
            total_init_time = (time.perf_counter() - init_start_time) * 1000

            print(f"[INIT] 手柄初始化成功: {joystick_name}")
            print(f"[INIT] 总初始化时间: {total_init_time:.1f} ms")
            return True
        except pygame.error as e:
            total_init_time = (time.perf_counter() - init_start_time) * 1000
            print(f"[INIT] 手柄初始化失败: {e} (耗时: {total_init_time:.1f} ms)")
            self.joystick = None
            self.initialized = False
            return False

    def get_data(self):
        """获取手柄轴和按钮数据"""
        if not self.initialized:
            return [], [], []

        axis_data = []
        key_data = []
        hat_data = []

        if self.joystick:
            # 获取原始轴数据
            raw_axis_data = []
            for i in range(self.joystick.get_numaxes()):
                axis = self.joystick.get_axis(i)
                raw_axis_data.append(axis)

            # 应用非线性平滑到轴数据
            axis_data = self.get_smoothed_axis_data(raw_axis_data)

            # 获取按钮数据
            for i in range(self.joystick.get_numbuttons()):
                button = self.joystick.get_button(i)
                key_data.append(button)

            # 读取帽键输入
            for i in range(self.joystick.get_numhats()):
                hat = self.joystick.get_hat(i)
                hat_data.append(hat)
        else:
            print("未检测到手柄")

        return axis_data, key_data, hat_data

    def set_start_pose(self, x=0.0, y=0.0, z=0.0, rx=0.0, ry=0.0, rz=0.0):
        """设置起点位置"""
        global START_POSE
        START_POSE['x'] = x
        START_POSE['y'] = y
        START_POSE['z'] = z
        START_POSE['rx'] = rx
        START_POSE['ry'] = ry
        START_POSE['rz'] = rz
        self.current_pose = START_POSE.copy()
        print(f"Start pose updated to: {START_POSE}")

    def reset_to_start(self):
        """重置到起点位置"""
        self.current_pose = START_POSE.copy()
        print(f"Reset to start pose: {self.current_pose}")

    def cleanup(self):
        """清理pygame资源"""
        if self.joystick:
            self.joystick.quit()
        pygame.joystick.quit()
        pygame.quit()
        self.initialized = False

def format_joystick_data_with_trajectory(axis_data, key_data, hat_data, controller):
    """带轨迹规划的数据格式化函数"""
    # 按钮状态
    start = int(key_data[2])   # start按钮
    suck = int(key_data[9])    # suck按钮 (M2)
    reset = int(key_data[3])   # reset按钮 (Y)
    record = int(key_data[1])  # record按钮

    # 检查紧急停止 (LB + RB)
    emergency_stop = (key_data[4] and key_data[5])

    if emergency_stop:
        controller.is_emergency_stop = True
        planned_velocity = controller.trajectory_planner.emergency_stop()
        print("[EMERGENCY] Emergency stop activated!")
    elif reset == 1:
        # 重置位置和轨迹规划器
        controller.update_pose_with_planned_velocity([0,0,0,0,0,0], reset_pressed=True)
        planned_velocity = [0,0,0,0,0,0]
        controller.is_emergency_stop = False
    else:
        controller.is_emergency_stop = False
        # 计算目标速度
        target_velocity = controller.calculate_target_velocity(axis_data, hat_data)

        # 轨迹规划
        planned_velocity = controller.trajectory_planner.plan_velocity_trajectory(target_velocity)

        # 更新位置
        controller.update_pose_with_planned_velocity(planned_velocity)

    # 获取当前绝对位置
    x = controller.current_pose['x']
    y = controller.current_pose['y']
    z = controller.current_pose['z']
    rx = controller.current_pose['rx']
    ry = controller.current_pose['ry']
    rz = controller.current_pose['rz']

    # 打包数据
    data = struct.pack('6d4i', x, y, z, rx, ry, rz, start, suck, reset, record)
    return data, planned_velocity

def format_joystick_data(axis_data, key_data, hat_data, current_pose):
    """标准数据格式化函数 (无轨迹规划)"""
    hat = hat_data[0] if hat_data else (0, 0)

    # 死区阈值处理
    newAxis = [0 if abs(element) <= deadZoneThreshold else element for element in axis_data]
    newAxis[4] = 0 if abs(axis_data[4]) <= XdeadZoneThreshold else axis_data[4]

    # 按钮状态
    start = int(key_data[2])
    suck = int(key_data[9])
    reset = int(key_data[3])
    record = int(key_data[1])

    # 检查Reset按钮，如果按下则重置到起点
    if reset == 1:
        current_pose.update(START_POSE)
    else:
        # 计算位置增量
        dx = (newAxis[4]*xScale - hat[1]*0.5)/1000.0
        dy = (newAxis[0]*yScale + hat[0]*0.5)/1000.0
        dz = (-(newAxis[2] + 1) + (newAxis[5] + 1))/ 2.0 *zScale/1000.0
        drx = 0.0
        dry = 0.0
        drz = 0.0

        # 累计到当前绝对位置
        current_pose['x'] += dx * TIME_INTERVAL
        current_pose['y'] += dy * TIME_INTERVAL
        current_pose['z'] += dz * TIME_INTERVAL
        current_pose['rx'] += drx * TIME_INTERVAL
        current_pose['ry'] += dry * TIME_INTERVAL
        current_pose['rz'] += drz * TIME_INTERVAL

    # 使用绝对位置
    x = current_pose['x']
    y = current_pose['y']
    z = current_pose['z']
    rx = current_pose['rx']
    ry = current_pose['ry']
    rz = current_pose['rz']

    # 打包数据
    data = struct.pack('6d4i', x, y, z, rx, ry, rz, start, suck, reset, record)
    return data

def format_joystick_data_json(axis_data, key_data, hat_data):
    """JSON格式数据 (用于调试)"""
    hat = hat_data[0] if hat_data else (0, 0)
    newAxis = [0 if abs(element) <= deadZoneThreshold else element for element in axis_data]
    newAxis[4] = 0 if abs(axis_data[4]) <= XdeadZoneThreshold else axis_data[4]

    # 计算位置增量
    x = (newAxis[4]*xScale - hat[1]*0.5)/1000.0
    y = (newAxis[0]*yScale + hat[0]*0.5)/1000.0
    z = (-(newAxis[2] + 1) + (newAxis[5] + 1))/ 2.0 *zScale/1000.0

    data = {
        "pose": [x, y, z, 0.0, 0.0, 0.0],
        "start": int(key_data[2]),
        "suck": int(key_data[9]),
        "reset": int(key_data[3]),
        "record": int(key_data[1]),
        "timestamp": time.time()
    }
    return json.dumps(data).encode('utf-8')

if __name__ == "__main__":
    program_start_time = time.perf_counter()
    print(f"[STARTUP] Program started at {time.strftime('%H:%M:%S', time.localtime())}")

    # 创建控制器实例
    controller = JoystickController()
    print(f"[STARTUP] Controller instance created")

    # 初始化手柄
    if not controller.fast_init():
        print("[STARTUP] 快速初始化失败，尝试标准初始化...")
        if not controller.init():
            print("未检测到手柄")
            exit(1)

    # 创建ZMQ发布者
    publisher = ZmqPublisher()
    publisher.connect()

    total_startup_time = (time.perf_counter() - program_start_time) * 1000
    print(f"[STARTUP] Total startup time: {total_startup_time:.1f} ms")
    print(f"[STARTUP] System ready, starting main loop...")

    # 状态变量
    prev_key_data_1_state = 0
    is_recording_started = False
    first_press_detected = False
    frame_count = 0
    fps_start_time = time.perf_counter()
    last_fps_print_time = fps_start_time

    try:
        print(f"[RUNTIME] Main loop started, target frequency: {1/TIME_INTERVAL:.1f} Hz")
        while True:
            startTime = time.perf_counter()
            frame_count += 1

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    raise KeyboardInterrupt

            axis_data, key_data, hat_data = controller.get_data()
            key_data_new = key_data.copy()

            # 处理记录状态切换
            current_key_data_1_state = key_data[1]
            if current_key_data_1_state == 1 and prev_key_data_1_state == 0:
                if not first_press_detected:
                    first_press_detected = True
                    is_recording_started = True
                    print("[RECORD] 记录开始")
                else:
                    is_recording_started = False
                    first_press_detected = False
                    print("[RECORD] 记录结束")
            prev_key_data_1_state = current_key_data_1_state

            # 设置记录状态
            if is_recording_started:
                key_data_new[1] = 2  # record = 2 表示正在记录
                controller.joystick.rumble(0.25, 0.25, int(TIME_INTERVAL*1000.0))
            else:
                key_data_new[1] = 0  # record = 0 表示不记录

            # 格式化并发送数据 (使用轨迹规划)
            send_start = time.perf_counter()
            binary_data, planned_velocity = format_joystick_data_with_trajectory(
                axis_data, key_data_new, hat_data, controller)
            publisher.send(binary_data)
            send_duration = (time.perf_counter() - send_start) * 1000

            # 计算并打印实时频率
            current_time = time.perf_counter()
            if current_time - last_fps_print_time >= 1.0:
                elapsed_time = current_time - fps_start_time
                actual_fps = frame_count / elapsed_time
                target_fps = 1 / TIME_INTERVAL
                fps_efficiency = (actual_fps / target_fps) * 100

                frame_count = 0
                fps_start_time = current_time
                last_fps_print_time = current_time

            # 打印调试信息 (包含规划速度)
            if frame_count % 10 == 0:
                print(f"P: [{controller.current_pose['x']*1000.0:.1f}, "
                      f"{controller.current_pose['y']*1000.0:.1f}, "
                      f"{controller.current_pose['z']*1000.0:.1f}] mm, "
                      f"V: [{planned_velocity[0]*1000:.1f}, "
                      f"{planned_velocity[1]*1000:.1f}, "
                      f"{planned_velocity[2]*1000:.1f}] mm/s, "
                      f"Btns:  S:{key_data_new[2]}  Su:{key_data_new[9]}  R:{key_data_new[3]}  Rec:{key_data_new[1]}")

            # 检查循环时间
            loop_duration = (time.perf_counter() - startTime) * 1000
            if loop_duration > TIME_INTERVAL * 1000 * 1.1:
                print(f"[WARNING] Loop time exceeded: {loop_duration:.2f} ms")

            accurate_delay_s(TIME_INTERVAL/2, startTime, TIME_INTERVAL)

    except KeyboardInterrupt:
        print("\n[SHUTDOWN] Received interrupt signal, exiting...")
    finally:
        shutdown_start = time.perf_counter()
        total_runtime = (shutdown_start - program_start_time)

        print(f"[SHUTDOWN] Total runtime: {total_runtime:.2f} seconds")

        # 清理资源
        controller.cleanup()
        publisher.close()

        total_shutdown_time = (time.perf_counter() - shutdown_start) * 1000
        print(f"[SHUTDOWN] Total shutdown time: {total_shutdown_time:.1f} ms")
        print("[SHUTDOWN] Program terminated successfully")
