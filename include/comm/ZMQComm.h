#ifndef ZMQ_COMM_H
#define ZMQ_COMM_H

#include "CommBase.h"
#include <zmq.hpp>
#include <functional>

class ZMQComm : public CommBase {
public:
    ZMQComm(bool is_server = false);
    ~ZMQComm();
    
    // 基础连接管理
    bool init() override;
    void close() override;
    bool isConnected() const override;

    // Request-Reply 模式：机器人通信层向硬件层请求执行功能指令
    std::string requestReply(const std::string& cmd) override;

    // 实时数据发送：机器人通信层发送给硬件板卡层，没有返回
    bool sendRtData(const ServoCommand& cmd) override;
    
    // 实时数据发送：硬件层发送给机器人通信层，没有返回
    bool sendRtData(const RobotState& state) override;

    // 实时数据接收：硬件板卡层发送给机器人通信层，回调函数形式
    bool recvRtData(const std::function<void(const ServoCommand&)>& callback) override;
    
    // 实时数据接收：机器人通信层接收硬件板卡层状态，回调函数形式
    bool recvRtData(const std::function<void(const RobotState&)>& callback) override;

    // 中断接收指令，返回结果：硬件板卡接收功能指令，根据枚举执行，返回结果
    std::string recvReply() override;

    // 发送回复：硬件板卡发送回复给机器人通信层
    bool sendReply(const std::string& reply);

    // // 反馈通道：硬件->机器人通信层
    bool sendFeedbackData(const std::string& data) override;
    bool recvFeedbackData(const std::function<void(const std::string&)>& callback) override;

    // 错误通道（error_pub/sub，string）
    bool sendError(const std::string& err);
    bool recvError(const std::function<void(const std::string&)>& callback);

    // 工具方法
    uint64_t getCurrentTimestamp() const override;
    std::string getConnectionInfo() const override;

private:
    bool is_server_;
    zmq::context_t ctx_;
    zmq::socket_t req_sock_;   // 指令 req/rep
    zmq::socket_t rep_sock_;
    zmq::socket_t servo_push_;  // 伺服命令 push/pull (Client推送，Server拉取)
    zmq::socket_t servo_pull_;
    zmq::socket_t feedback_pub_; // 反馈 pub/sub
    zmq::socket_t feedback_sub_;
    zmq::socket_t error_pub_;  // 异常 pub/sub
    zmq::socket_t error_sub_;

    // 连接测试状态
    bool is_conncet_ = false;

    // IPC端点 - 根据通信表规范
    static constexpr const char* cmd_endpoint = "ipc:///tmp/mytest_cmd";//mytest_
    static constexpr const char* servo_data_endpoint = "ipc:///tmp/mytest_servo_data";
    //static constexpr const char* servo_data_endpoint = "ipc:///tmp/mytest_servo_data";
    static constexpr const char* feedback_data_endpoint = "ipc:///tmp/mytest_feedback_data";
    static constexpr const char* error_endpoint = "ipc:///tmp/mytest_error";
};

#endif // ZMQ_COMM_H 