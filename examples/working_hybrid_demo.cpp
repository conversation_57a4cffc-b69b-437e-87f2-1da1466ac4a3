#include "trajectory/HybridTrajectoryPlanner.hpp"
#include <iostream>
#include <vector>
#include <thread>
#include <chrono>
#include <iomanip>

constexpr int DOF = 6;

void demonstrateScenario1() {
    std::cout << "\n=== 场景1：离线规划A->B->C轨迹 ===" << std::endl;
    
    // 创建混合轨迹规划器
    HybridTrajectoryPlanner<DOF> planner(0.001);
    
    // 设置约束
    MotionConstraints<DOF> constraints;
    constraints.max_velocity.setConstant(1.0);
    constraints.max_acceleration.setConstant(2.0);
    constraints.max_jerk.setConstant(10.0);
    planner.setConstraints(constraints);
    
    if (!planner.start()) {
        std::cerr << "启动规划器失败" << std::endl;
        return;
    }
    
    // 定义A->B->C轨迹
    std::vector<MotionState<DOF>> waypoints_abc;
    
    // 点A (起点)
    MotionState<DOF> point_a;
    point_a.position << 0.0, 0.0, 0.0, 0.0, 0.0, 0.0;
    point_a.velocity.setZero();
    point_a.acceleration.setZero();
    waypoints_abc.push_back(point_a);
    
    // 点B (中间点)
    MotionState<DOF> point_b;
    point_b.position << 0.5, 0.3, -0.2, 0.1, 0.4, -0.1;
    point_b.velocity.setZero();
    point_b.acceleration.setZero();
    waypoints_abc.push_back(point_b);
    
    // 点C (终点)
    MotionState<DOF> point_c;
    point_c.position << 1.0, 0.5, -0.5, 0.3, 0.8, -0.3;
    point_c.velocity.setZero();
    point_c.acceleration.setZero();
    waypoints_abc.push_back(point_c);
    
    // 设置初始状态
    planner.updateCurrentState(point_a);
    
    // 添加并执行A->B->C轨迹
    if (planner.addTrajectorySegment(waypoints_abc, true)) {
        std::cout << "成功启动A->B->C轨迹规划" << std::endl;
        
        // 实时跟踪轨迹执行
        double time = 0.0;
        double dt = 0.2;  // 200ms间隔
        
        for (int i = 0; i < 25; ++i) {  // 5秒
            auto target = planner.getCurrentTarget(time);
            
            if (target.valid) {
                std::cout << "t=" << std::fixed << std::setprecision(1) << time 
                          << "s, pos=[" << std::setprecision(2);
                for (int j = 0; j < DOF; ++j) {
                    std::cout << target.position[j];
                    if (j < DOF-1) std::cout << ", ";
                }
                std::cout << "], queue=" << planner.getQueueSize() << std::endl;
            }
            
            time += dt;
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }
    }
    
    planner.stop();
    std::cout << "场景1完成\n" << std::endl;
}

void demonstrateScenario2() {
    std::cout << "\n=== 场景2：动态轨迹切换D->E->F ===" << std::endl;
    
    HybridTrajectoryPlanner<DOF> planner(0.001);
    
    MotionConstraints<DOF> constraints;
    constraints.max_velocity.setConstant(1.0);
    constraints.max_acceleration.setConstant(2.0);
    constraints.max_jerk.setConstant(10.0);
    planner.setConstraints(constraints);
    
    if (!planner.start()) {
        std::cerr << "启动规划器失败" << std::endl;
        return;
    }
    
    // 初始轨迹：从原点到[0.5, 0.5, ...]
    std::vector<MotionState<DOF>> initial_waypoints;
    
    MotionState<DOF> start;
    start.position.setZero();
    start.velocity.setZero();
    start.acceleration.setZero();
    initial_waypoints.push_back(start);
    
    MotionState<DOF> mid;
    mid.position.setConstant(0.5);
    mid.velocity.setZero();
    mid.acceleration.setZero();
    initial_waypoints.push_back(mid);
    
    planner.updateCurrentState(start);
    planner.addTrajectorySegment(initial_waypoints, true);
    
    std::cout << "开始执行初始轨迹..." << std::endl;
    
    // 执行2秒后切换轨迹
    double time = 0.0;
    double dt = 0.2;
    
    for (int i = 0; i < 10; ++i) {  // 2秒
        auto target = planner.getCurrentTarget(time);
        
        if (target.valid) {
            std::cout << "t=" << std::fixed << std::setprecision(1) << time 
                      << "s, pos=[" << std::setprecision(2);
            for (int j = 0; j < DOF; ++j) {
                std::cout << target.position[j];
                if (j < DOF-1) std::cout << ", ";
            }
            std::cout << "]" << std::endl;
            
            // 更新当前状态（模拟机器人反馈）
            MotionState<DOF> current_state;
            current_state.position = target.position;
            current_state.velocity = target.velocity;
            current_state.acceleration = target.acceleration;
            planner.updateCurrentState(current_state);
        }
        
        time += dt;
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    
    // 动态切换到新轨迹D->E->F
    std::cout << "\n>>> 动态切换到新轨迹D->E->F <<<" << std::endl;
    
    // 获取当前状态作为D点
    auto current_target = planner.getCurrentTarget(time);
    
    std::vector<MotionState<DOF>> new_waypoints;
    
    // 点D (当前位置)
    MotionState<DOF> point_d;
    point_d.position = current_target.position;
    point_d.velocity.setZero();
    point_d.acceleration.setZero();
    new_waypoints.push_back(point_d);
    
    // 点E (新中间点)
    MotionState<DOF> point_e;
    point_e.position = point_d.position;
    point_e.position[0] += 0.3;
    point_e.position[1] -= 0.2;
    point_e.position[2] += 0.4;
    point_e.velocity.setZero();
    point_e.acceleration.setZero();
    new_waypoints.push_back(point_e);
    
    // 点F (新终点)
    MotionState<DOF> point_f;
    point_f.position = point_e.position;
    point_f.position[0] += 0.4;
    point_f.position[1] += 0.3;
    point_f.position[2] -= 0.2;
    point_f.velocity.setZero();
    point_f.acceleration.setZero();
    new_waypoints.push_back(point_f);
    
    // 请求轨迹切换
    if (planner.requestTrajectoryTransition(new_waypoints, 0.5)) {
        std::cout << "成功切换到D->E->F轨迹" << std::endl;
        
        // 继续执行新轨迹
        for (int i = 0; i < 20; ++i) {  // 4秒
            auto target = planner.getCurrentTarget(time);
            
            if (target.valid) {
                std::cout << "t=" << std::fixed << std::setprecision(1) << time 
                          << "s, pos=[" << std::setprecision(2);
                for (int j = 0; j < DOF; ++j) {
                    std::cout << target.position[j];
                    if (j < DOF-1) std::cout << ", ";
                }
                std::cout << "], transitioning=" << planner.isTransitioning() << std::endl;
            }
            
            time += dt;
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }
    }
    
    planner.stop();
    std::cout << "场景2完成\n" << std::endl;
}

void demonstrateScenario3() {
    std::cout << "\n=== 场景3：紧急停止演示 ===" << std::endl;
    
    HybridTrajectoryPlanner<DOF> planner(0.001);
    
    MotionConstraints<DOF> constraints;
    constraints.max_velocity.setConstant(2.0);  // 更高速度
    constraints.max_acceleration.setConstant(4.0);
    constraints.max_jerk.setConstant(20.0);
    planner.setConstraints(constraints);
    
    if (!planner.start()) {
        std::cerr << "启动规划器失败" << std::endl;
        return;
    }
    
    // 创建长距离轨迹
    std::vector<MotionState<DOF>> waypoints;
    
    MotionState<DOF> start;
    start.position.setZero();
    start.velocity.setZero();
    start.acceleration.setZero();
    waypoints.push_back(start);
    
    MotionState<DOF> end;
    end.position.setConstant(2.0);  // 长距离移动
    end.velocity.setZero();
    end.acceleration.setZero();
    waypoints.push_back(end);
    
    planner.updateCurrentState(start);
    planner.addTrajectorySegment(waypoints, true);
    
    std::cout << "开始执行长距离轨迹..." << std::endl;
    
    double time = 0.0;
    double dt = 0.2;
    
    // 执行1秒后触发紧急停止
    for (int i = 0; i < 5; ++i) {
        auto target = planner.getCurrentTarget(time);
        
        if (target.valid) {
            std::cout << "t=" << std::fixed << std::setprecision(1) << time 
                      << "s, pos=[" << std::setprecision(2);
            for (int j = 0; j < DOF; ++j) {
                std::cout << target.position[j];
                if (j < DOF-1) std::cout << ", ";
            }
            std::cout << "]" << std::endl;
        }
        
        time += dt;
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    
    // 触发紧急停止
    std::cout << "\n>>> 触发紧急停止 <<<" << std::endl;
    if (planner.emergencyStop()) {
        std::cout << "紧急停止成功" << std::endl;
        
        // 观察停止过程
        for (int i = 0; i < 10; ++i) {
            auto target = planner.getCurrentTarget(time);
            
            if (target.valid) {
                std::cout << "t=" << std::fixed << std::setprecision(1) << time 
                          << "s, pos=[" << std::setprecision(2);
                for (int j = 0; j < DOF; ++j) {
                    std::cout << target.position[j];
                    if (j < DOF-1) std::cout << ", ";
                }
                std::cout << "], vel_norm=" << target.velocity.norm() << std::endl;
            }
            
            time += dt;
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }
    }
    
    planner.stop();
    std::cout << "场景3完成\n" << std::endl;
}

int main() {
    std::cout << "=== 混合轨迹控制完整演示 ===" << std::endl;
    
    try {
        demonstrateScenario1();  // 离线规划A->B->C
        demonstrateScenario2();  // 动态切换D->E->F
        demonstrateScenario3();  // 紧急停止
        
        std::cout << "所有演示完成！混合轨迹控制系统工作正常！🚀" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "演示过程中发生错误: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
