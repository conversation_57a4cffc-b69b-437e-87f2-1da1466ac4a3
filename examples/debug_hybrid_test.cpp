#include "trajectory/HybridTrajectoryPlanner.hpp"
#include <iostream>
#include <vector>
#include <thread>
#include <chrono>
#include <iomanip>

constexpr int DOF = 6;

int main() {
    std::cout << "=== 调试混合轨迹规划器 ===" << std::endl;

    // 创建混合轨迹规划器
    HybridTrajectoryPlanner<DOF> planner(0.001);

    // 设置约束
    MotionConstraints<DOF> constraints;
    constraints.max_velocity.setConstant(1.0);
    constraints.max_acceleration.setConstant(2.0);
    constraints.max_jerk.setConstant(10.0);
    planner.setConstraints(constraints);

    // 启动规划器
    if (!planner.start()) {
        std::cerr << "启动规划器失败" << std::endl;
        return -1;
    }

    // 创建简单的两点轨迹
    std::vector<MotionState<DOF>> waypoints;
    
    // 起点
    MotionState<DOF> start;
    start.position.setZero();
    start.velocity.setZero();
    start.acceleration.setZero();
    waypoints.push_back(start);

    // 终点
    MotionState<DOF> end;
    end.position.setConstant(1.0);  // 移动到[1,1,1,1,1,1]
    end.velocity.setZero();
    end.acceleration.setZero();
    waypoints.push_back(end);

    // 设置初始状态
    planner.updateCurrentState(start);

    std::cout << "添加轨迹段..." << std::endl;
    
    // 添加轨迹段
    if (planner.addTrajectorySegment(waypoints, true)) {  // immediate=true
        std::cout << "轨迹段添加成功，开始执行..." << std::endl;

        // 模拟执行
        double time = 0.0;
        double dt = 0.1;  // 100ms
        
        for (int i = 0; i < 50; ++i) {  // 5秒
            auto target = planner.getCurrentTarget(time);
            
            if (target.valid) {
                std::cout << "t=" << std::fixed << std::setprecision(1) << time 
                          << "s, pos=[" << std::setprecision(3) 
                          << target.position[0] << ", " << target.position[1] << ", " 
                          << target.position[2] << ", " << target.position[3] << ", " 
                          << target.position[4] << ", " << target.position[5] << "]"
                          << ", queue=" << planner.getQueueSize()
                          << ", active=" << planner.isActive() << std::endl;
            } else {
                std::cout << "t=" << time << "s, INVALID TARGET" << std::endl;
            }
            
            time += dt;
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    } else {
        std::cerr << "轨迹段添加失败" << std::endl;
    }

    planner.stop();
    std::cout << "测试完成" << std::endl;
    return 0;
}
