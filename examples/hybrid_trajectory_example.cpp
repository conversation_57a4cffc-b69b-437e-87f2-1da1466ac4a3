#include "trajectory/HybridTrajectoryManager.hpp"
#include <iostream>
#include <vector>
#include <thread>
#include <chrono>
#include <iomanip>
#include <sstream>

// 机器人实时控制混合轨迹示例
// 演示场景：
// 1. 离线规划A->B->C轨迹
// 2. 在线跟踪执行
// 3. 动态切换到D->E->F轨迹

constexpr int DOF = 6;  // 6自由度机器人

class HybridTrajectoryExample {
private:
    std::shared_ptr<HybridTrajectoryManager<DOF>> manager_;
    bool simulation_running_ = false;
    std::thread simulation_thread_;

public:
    HybridTrajectoryExample() {
        // 创建混合轨迹管理器
        setupManager();
    }

    ~HybridTrajectoryExample() {
        stop();
    }

    void setupManager() {
        // 配置运动约束
        MotionConstraints<DOF> constraints;
        constraints.max_velocity.setConstant(1.0);      // 1 rad/s
        constraints.max_acceleration.setConstant(2.0);  // 2 rad/s²
        constraints.max_jerk.setConstant(10.0);         // 10 rad/s³

        // 创建管理器
        manager_ = HybridTrajectoryManagerFactory<DOF>::createForRobot(
            "", constraints, 1000.0);  // 1kHz控制频率

        // 设置回调
        manager_->setEventCallback([this](const HybridManagerEvent<DOF>& event) {
            handleEvent(event);
        });

        manager_->setStateCallback([this](const TrajectoryState<DOF>& state) {
            handleStateUpdate(state);
        });
    }

    void handleEvent(const HybridManagerEvent<DOF>& event) {
        switch (event.type) {
            case HybridManagerEvent<DOF>::MODE_CHANGED:
                std::cout << "[EVENT] Mode changed: " << event.message << std::endl;
                break;
            case HybridManagerEvent<DOF>::SEGMENT_COMPLETED:
                std::cout << "[EVENT] Segment " << event.segment_id << " completed" << std::endl;
                break;
            case HybridManagerEvent<DOF>::TRAJECTORY_UPDATED:
                std::cout << "[EVENT] Trajectory updated: " << event.message << std::endl;
                break;
            case HybridManagerEvent<DOF>::TRANSITION_STARTED:
                std::cout << "[EVENT] Transition started" << std::endl;
                break;
            case HybridManagerEvent<DOF>::EMERGENCY_TRIGGERED:
                std::cout << "[EVENT] EMERGENCY: " << event.message << std::endl;
                break;
            default:
                std::cout << "[EVENT] " << event.message << std::endl;
                break;
        }
    }

    void handleStateUpdate(const TrajectoryState<DOF>& state) {
        // 模拟机器人状态更新（实际应用中这里会发送到机器人控制器）
        static int update_count = 0;
        if (++update_count % 1000 == 0) {  // 每秒打印一次
            std::cout << "[STATE] t=" << std::fixed << std::setprecision(3) 
                      << state.timestamp << "s, pos=[";
            for (int i = 0; i < DOF; ++i) {
                std::cout << std::setprecision(2) << state.position[i];
                if (i < DOF - 1) std::cout << ", ";
            }
            std::cout << "]" << std::endl;
        }
    }

    // 场景1：离线规划A->B->C轨迹
    void demonstrateOfflinePlanning() {
        std::cout << "\n=== 场景1：离线规划A->B->C轨迹 ===" << std::endl;

        // 定义航向点A, B, C
        std::vector<MotionState<DOF>> waypoints_abc;
        
        // 点A (起点)
        MotionState<DOF> point_a;
        point_a.position << 0.0, 0.0, 0.0, 0.0, 0.0, 0.0;
        point_a.velocity.setZero();
        point_a.acceleration.setZero();
        waypoints_abc.push_back(point_a);

        // 点B (中间点)
        MotionState<DOF> point_b;
        point_b.position << 0.5, 0.3, -0.2, 0.1, 0.4, -0.1;
        point_b.velocity.setZero();
        point_b.acceleration.setZero();
        waypoints_abc.push_back(point_b);

        // 点C (终点)
        MotionState<DOF> point_c;
        point_c.position << 1.0, 0.5, -0.5, 0.3, 0.8, -0.3;
        point_c.velocity.setZero();
        point_c.acceleration.setZero();
        waypoints_abc.push_back(point_c);

        // 设置初始状态
        manager_->updateCurrentState(point_a);

        // 切换到混合模式并执行A->B->C轨迹
        if (manager_->switchToHybridMode(waypoints_abc)) {
            std::cout << "成功启动A->B->C轨迹规划" << std::endl;
            
            // 等待轨迹执行一段时间
            std::this_thread::sleep_for(std::chrono::seconds(3));
        } else {
            std::cout << "A->B->C轨迹规划失败" << std::endl;
        }
    }

    // 场景2：动态切换到D->E->F轨迹
    void demonstrateDynamicSwitching() {
        std::cout << "\n=== 场景2：动态切换到D->E->F轨迹 ===" << std::endl;

        // 获取当前状态作为D点
        MotionState<DOF> current_state = manager_->getCurrentState();
        std::cout << "当前位置D: [";
        for (int i = 0; i < DOF; ++i) {
            std::cout << std::setprecision(2) << current_state.position[i];
            if (i < DOF - 1) std::cout << ", ";
        }
        std::cout << "]" << std::endl;

        // 定义新的航向点D->E->F
        std::vector<MotionState<DOF>> waypoints_def;
        
        // 点D (当前位置)
        waypoints_def.push_back(current_state);

        // 点E (新中间点)
        MotionState<DOF> point_e;
        point_e.position = current_state.position;
        point_e.position[0] += 0.3;  // X方向移动
        point_e.position[1] -= 0.2;  // Y方向移动
        point_e.position[2] += 0.4;  // Z方向移动
        point_e.velocity.setZero();
        point_e.acceleration.setZero();
        waypoints_def.push_back(point_e);

        // 点F (新终点)
        MotionState<DOF> point_f;
        point_f.position = point_e.position;
        point_f.position[0] += 0.4;
        point_f.position[1] += 0.3;
        point_f.position[2] -= 0.2;
        point_f.velocity.setZero();
        point_f.acceleration.setZero();
        waypoints_def.push_back(point_f);

        // 请求轨迹切换
        if (manager_->requestTrajectorySwitch(waypoints_def, 0.5)) {
            std::cout << "成功切换到D->E->F轨迹" << std::endl;
            
            // 等待新轨迹执行
            std::this_thread::sleep_for(std::chrono::seconds(4));
        } else {
            std::cout << "D->E->F轨迹切换失败" << std::endl;
        }
    }

    // 场景3：在线目标跟踪
    void demonstrateOnlineTracking() {
        std::cout << "\n=== 场景3：在线目标跟踪 ===" << std::endl;

        // 切换到在线模式
        if (manager_->switchToMode(HybridTrajectoryMode::ONLINE)) {
            std::cout << "切换到在线模式成功" << std::endl;

            // 设置一系列在线目标
            for (int i = 0; i < 5; ++i) {
                MotionState<DOF> target;
                target.position.setRandom() * 0.5;  // 随机目标位置
                target.velocity.setZero();
                target.acceleration.setZero();

                std::cout << "设置在线目标 " << i + 1 << ": [";
                for (int j = 0; j < DOF; ++j) {
                    std::cout << std::setprecision(2) << target.position[j];
                    if (j < DOF - 1) std::cout << ", ";
                }
                std::cout << "]" << std::endl;

                manager_->setOnlineTarget(target);
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
        }
    }

    // 场景4：紧急停止
    void demonstrateEmergencyStop() {
        std::cout << "\n=== 场景4：紧急停止 ===" << std::endl;

        std::cout << "触发紧急停止..." << std::endl;
        if (manager_->emergencyStop()) {
            std::cout << "紧急停止成功" << std::endl;
            
            // 等待停止完成
            std::this_thread::sleep_for(std::chrono::seconds(2));
            
            // 清除紧急状态
            manager_->clearEmergency();
            std::cout << "紧急状态已清除" << std::endl;
        }
    }

    void run() {
        std::cout << "启动混合轨迹控制示例..." << std::endl;

        // 启动管理器
        if (!manager_->start()) {
            std::cerr << "启动管理器失败" << std::endl;
            return;
        }

        // 启动模拟线程
        simulation_running_ = true;
        simulation_thread_ = std::thread(&HybridTrajectoryExample::simulationLoop, this);

        // 执行演示场景
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        demonstrateOfflinePlanning();
        demonstrateDynamicSwitching();
        demonstrateOnlineTracking();
        demonstrateEmergencyStop();

        std::cout << "\n示例演示完成" << std::endl;
        manager_->printStatus();
    }

    void stop() {
        simulation_running_ = false;
        if (simulation_thread_.joinable()) {
            simulation_thread_.join();
        }
        if (manager_) {
            manager_->stop();
        }
    }

private:
    void simulationLoop() {
        // 模拟机器人状态反馈循环
        while (simulation_running_) {
            // 这里可以模拟机器人的实际状态反馈
            // 在实际应用中，这里会从机器人获取真实状态
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }
};

int main() {
    try {
        HybridTrajectoryExample example;
        example.run();
        
        std::cout << "\n按任意键退出..." << std::endl;
        std::cin.get();
        
        example.stop();
        
    } catch (const std::exception& e) {
        std::cerr << "示例运行出错: " << e.what() << std::endl;
        return -1;
    }

    return 0;
}
