#!/usr/bin/env python3
"""
可视化混合轨迹测试结果
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import sys
import os

def load_trajectory_data(csv_file):
    """加载轨迹数据"""
    try:
        data = pd.read_csv(csv_file)
        return data
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def plot_trajectory_segments(data):
    """绘制轨迹段"""
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('混合轨迹测试结果', fontsize=16)
    
    # 获取不同段的数据
    segment1_data = data[data['segment_id'] == 1]
    segment2_data = data[data['segment_id'] == 2]
    segment3_data = data[data['segment_id'] == 3]
    
    # 绘制前3个轴的位置
    for i in range(3):
        ax = axes[0, i]
        ax.set_title(f'轴 {i+1} 位置')
        ax.set_xlabel('时间 (s)')
        ax.set_ylabel('位置 (rad)')
        
        if not segment1_data.empty:
            ax.plot(segment1_data['timestamp'], segment1_data[f'pos_{i+1}'], 
                   'b-', label='段1: A->B->C', linewidth=2)
        
        if not segment2_data.empty:
            ax.plot(segment2_data['timestamp'], segment2_data[f'pos_{i+1}'], 
                   'r-', label='段2: sin曲线', linewidth=2)
        
        if not segment3_data.empty:
            ax.plot(segment3_data['timestamp'], segment3_data[f'pos_{i+1}'], 
                   'g-', label='段3: C2->D->E->F', linewidth=2)
        
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    # 绘制前3个轴的速度
    for i in range(3):
        ax = axes[1, i]
        ax.set_title(f'轴 {i+1} 速度')
        ax.set_xlabel('时间 (s)')
        ax.set_ylabel('速度 (rad/s)')
        
        if not segment1_data.empty:
            ax.plot(segment1_data['timestamp'], segment1_data[f'vel_{i+1}'], 
                   'b-', label='段1: A->B->C', linewidth=2)
        
        if not segment2_data.empty:
            ax.plot(segment2_data['timestamp'], segment2_data[f'vel_{i+1}'], 
                   'r-', label='段2: sin曲线', linewidth=2)
        
        if not segment3_data.empty:
            ax.plot(segment3_data['timestamp'], segment3_data[f'vel_{i+1}'], 
                   'g-', label='段3: C2->D->E->F', linewidth=2)
        
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    return fig

def plot_sin_trajectory_detail(data):
    """详细绘制sin曲线轨迹"""
    segment2_data = data[data['segment_id'] == 2]
    
    if segment2_data.empty:
        print("没有找到sin曲线轨迹数据")
        return None
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('Sin曲线轨迹详细分析', fontsize=16)
    
    # 绘制所有6个轴的位置
    for i in range(6):
        row = i // 3
        col = i % 3
        ax = axes[row, col]
        
        ax.plot(segment2_data['timestamp'], segment2_data[f'pos_{i+1}'], 
               'r-', linewidth=2, label=f'轴{i+1}位置')
        ax.set_title(f'轴 {i+1} Sin曲线轨迹')
        ax.set_xlabel('时间 (s)')
        ax.set_ylabel('位置 (rad)')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    return fig

def analyze_trajectory_continuity(data):
    """分析轨迹连续性"""
    print("\n=== 轨迹连续性分析 ===")
    
    # 计算位置跳跃
    pos_cols = [f'pos_{i}' for i in range(1, 7)]
    vel_cols = [f'vel_{i}' for i in range(1, 7)]
    
    pos_diff = data[pos_cols].diff().abs()
    vel_diff = data[vel_cols].diff().abs()
    
    max_pos_jump = pos_diff.max().max()
    max_vel_jump = vel_diff.max().max()
    
    print(f"最大位置跳跃: {max_pos_jump:.6f}")
    print(f"最大速度跳跃: {max_vel_jump:.6f}")
    
    # 分析段间连接
    segments = data['segment_id'].unique()
    for i in range(len(segments)-1):
        seg1_end = data[data['segment_id'] == segments[i]].iloc[-1]
        seg2_start = data[data['segment_id'] == segments[i+1]].iloc[0]
        
        pos_jump = np.linalg.norm([seg2_start[f'pos_{j}'] - seg1_end[f'pos_{j}'] for j in range(1, 7)])
        vel_jump = np.linalg.norm([seg2_start[f'vel_{j}'] - seg1_end[f'vel_{j}'] for j in range(1, 7)])
        
        print(f"段{segments[i]}->段{segments[i+1]} 位置跳跃: {pos_jump:.6f}, 速度跳跃: {vel_jump:.6f}")

def print_statistics(data):
    """打印统计信息"""
    print("\n=== 轨迹统计信息 ===")
    print(f"总数据点数: {len(data)}")
    print(f"总执行时间: {data['timestamp'].max() - data['timestamp'].min():.3f} 秒")
    print(f"平均采样频率: {len(data) / (data['timestamp'].max() - data['timestamp'].min()):.1f} Hz")
    
    segments = data['segment_id'].unique()
    for seg in segments:
        seg_data = data[data['segment_id'] == seg]
        duration = seg_data['timestamp'].max() - seg_data['timestamp'].min()
        print(f"段{seg}: {len(seg_data)} 点, 持续时间: {duration:.3f} 秒")

def main():
    # 检查CSV文件
    csv_file = 'hybrid_trajectory_log.csv'
    if not os.path.exists(csv_file):
        print(f"找不到文件: {csv_file}")
        print("请确保在build目录下运行此脚本")
        return
    
    # 加载数据
    data = load_trajectory_data(csv_file)
    if data is None:
        return
    
    # 打印统计信息
    print_statistics(data)
    
    # 分析连续性
    analyze_trajectory_continuity(data)
    
    # 绘制图表
    fig1 = plot_trajectory_segments(data)
    fig2 = plot_sin_trajectory_detail(data)
    
    # 保存图片
    if fig1:
        fig1.savefig('hybrid_trajectory_overview.png', dpi=300, bbox_inches='tight')
        print("\n轨迹概览图已保存为: hybrid_trajectory_overview.png")
    
    if fig2:
        fig2.savefig('sin_trajectory_detail.png', dpi=300, bbox_inches='tight')
        print("Sin曲线详细图已保存为: sin_trajectory_detail.png")
    
    # 显示图表
    plt.show()

if __name__ == "__main__":
    main()
