#include "trajectory/HybridTrajectoryPlanner.hpp"
#include "trajectory/TrajInterpolatorBase.hpp"
#include <iostream>
#include <thread>
#include <chrono>
#include <vector>
#include <cmath>
#include <fstream>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <iomanip>

constexpr int DOF = 6;
constexpr double CONTROL_FREQ = 1000.0;  // 1kHz
constexpr double DT = 1.0 / CONTROL_FREQ;

class HybridTrajectoryTest {
private:
    std::unique_ptr<HybridTrajectoryPlanner<DOF>> planner_;
    
    // 预定义点位
    MotionState<DOF> point_A_, point_B_, point_C_;
    MotionState<DOF> point_D_, point_E_, point_F_;
    
    // 线程控制
    std::atomic<bool> is_running_{false};
    std::atomic<bool> segment1_completed_{false};
    std::atomic<bool> segment2_completed_{false};
    std::atomic<bool> segment3_completed_{false};
    std::mutex test_mutex_;
    std::condition_variable cv_;
    
    // 记录数据
    std::vector<TrajectoryState<DOF>> recorded_trajectory_;
    std::ofstream log_file_;
    
    // 统计数据
    std::atomic<size_t> execution_count_{0};
    std::chrono::steady_clock::time_point start_time_;

public:
    HybridTrajectoryTest() {
        initializePoints();
        log_file_.open("hybrid_trajectory_log.csv");
        log_file_ << "timestamp,segment_id,pos_1,pos_2,pos_3,pos_4,pos_5,pos_6,"
                  << "vel_1,vel_2,vel_3,vel_4,vel_5,vel_6\n";
    }
    
    ~HybridTrajectoryTest() {
        if (log_file_.is_open()) {
            log_file_.close();
        }
    }
    
    bool initialize() {
        std::cout << "=== 初始化混合轨迹测试 ===" << std::endl;
        
        // 创建规划器
        planner_ = std::make_unique<HybridTrajectoryPlanner<DOF>>(DT);
        
        // 设置运动约束
        MotionConstraints<DOF> constraints;
        constraints.max_velocity.setConstant(5.0);      // rad/s
        constraints.max_acceleration.setConstant(10.0);  // rad/s²
        constraints.max_jerk.setConstant(50.0);         // rad/s³
        planner_->setConstraints(constraints);
        
        // 设置回调函数
        planner_->setCompletionCallback([this](size_t segment_id) {
            onSegmentCompleted(segment_id);
        });
        
        // 启动规划器
        if (!planner_->start()) {
            std::cerr << "启动规划器失败" << std::endl;
            return false;
        }
        
        // 设置初始状态为点A
        planner_->updateCurrentState(point_A_);
        
        printTestPlan();
        return true;
    }
    
    void runTest() {
        std::cout << "\n=== 开始混合轨迹测试 ===" << std::endl;
        
        is_running_ = true;
        start_time_ = std::chrono::steady_clock::now();
        
        // 启动两个线程
        std::thread feeder_thread(&HybridTrajectoryTest::trajectoryFeederThread, this);
        std::thread executor_thread(&HybridTrajectoryTest::trajectoryExecutorThread, this);
        
        // 等待测试完成
        feeder_thread.join();
        executor_thread.join();
        
        // 分析结果
        analyzeResults();
        
        std::cout << "\n=== 混合轨迹测试完成 ===" << std::endl;
    }

private:
    void initializePoints() {
        // A (0,0,0,0,0,0)
        point_A_.position.setZero();
        point_A_.velocity.setZero();
        point_A_.acceleration.setZero();
        
        // B (1,1,1,1,1,1)
        point_B_.position.setOnes();
        point_B_.velocity.setZero();
        point_B_.acceleration.setZero();
        
        // C (2,2,2,2,2,2)
        point_C_.position.setConstant(2.0);
        point_C_.velocity.setZero();
        point_C_.acceleration.setZero();
        
        // D (2,2,2,2,2,2) - 将在sin曲线结束后更新
        point_D_.position.setConstant(2.0);
        point_D_.velocity.setZero();
        point_D_.acceleration.setZero();
        
        // E (4,4,4,4,4,4)
        point_E_.position.setConstant(4.0);
        point_E_.velocity.setZero();
        point_E_.acceleration.setZero();
        
        // F (6,6,6,6,6,6)
        point_F_.position.setConstant(6.0);
        point_F_.velocity.setZero();
        point_F_.acceleration.setZero();
    }
    
    std::vector<MotionState<DOF>> generateSinTrajectory() {
        std::cout << "生成sin曲线轨迹 (C->C1->C2, 2秒)" << std::endl;

        const double duration = 2.0;  // 2秒
        const double freq = 0.5;      // 0.5Hz，降低频率
        const double sample_dt = 0.1;  // 100ms采样间隔，减少点数
        const int num_points = static_cast<int>(duration / sample_dt) + 1;

        std::vector<MotionState<DOF>> trajectory;
        trajectory.reserve(num_points);

        for (int i = 0; i < num_points; ++i) {
            double t = i * sample_dt;
            MotionState<DOF> state;

            // 为每个轴生成不同的sin曲线
            for (int j = 0; j < DOF; ++j) {
                double amplitude = 0.2 * (1.0 );  // 减小幅度//- j * 0.05
                double phase =  M_PI / 6.0;               // 不同相位j *
                double omega = 2.0 * M_PI * freq;

                // 位置: C[j] + amplitude * sin(omega*t + phase)
                state.position[j] = point_C_.position[j] + amplitude * std::sin(omega * t + phase);

                // 速度: amplitude * omega * cos(omega*t + phase)
                state.velocity[j] = amplitude * omega * std::cos(omega * t + phase);

                // 加速度: -amplitude * omega^2 * sin(omega*t + phase)
                state.acceleration[j] = -amplitude * omega * omega * std::sin(omega * t + phase);
            }

            trajectory.push_back(state);
        }

        // 更新D点为sin曲线的终点
        if (!trajectory.empty()) {
            point_D_ = trajectory.back();
            point_D_.velocity.setZero();
            point_D_.acceleration.setZero();
        }

        std::cout << "生成了 " << trajectory.size() << " 个sin轨迹点" << std::endl;
        return trajectory;
    }
    
    void trajectoryFeederThread() {
        std::cout << "[线程1] 轨迹添加线程启动" << std::endl;

        
        // 段1: A->B->C 离线点位
        std::cout << "[线程1] 添加段1: A->B->C" << std::endl;
        std::vector<MotionState<DOF>> waypoints1 = {point_A_, point_B_, point_C_};
        if (!planner_->addTrajectorySegment(waypoints1)) {
            std::cerr << "[线程1] 添加段1失败" << std::endl;
            return;
        }
        
        // 等待段1完成
        {
            std::unique_lock<std::mutex> lock(test_mutex_);
            cv_.wait(lock, [this] { return segment1_completed_.load(); });
            std::cout << "[线程1] 段1完成" << std::endl;
        }
        
        // 段2: C->C1->C2 sin曲线
        std::cout << "[线程1] 添加段2: sin曲线轨迹" << std::endl;
        auto sin_trajectory = generateSinTrajectory();

        // 打印轨迹点信息
        std::cout << "轨迹点示例:" << std::endl;
        for (int i = 0; i < std::min(3, static_cast<int>(sin_trajectory.size())); ++i) {
            std::cout << "  点" << i << ": 位置=" << sin_trajectory[i].position.transpose()
                      << ", 速度=" << sin_trajectory[i].velocity.transpose() << std::endl;
        }

        if (!planner_->addTrajectorySegment(sin_trajectory)) {
            std::cerr << "[线程1] 添加段2失败" << std::endl;
            return;
        }

        std::cout << "[线程1] 段2添加成功" << std::endl;
        
        // 等待段2完成
        {
            std::unique_lock<std::mutex> lock(test_mutex_);
            cv_.wait(lock, [this] { return segment2_completed_.load(); });
        }
        
        // 段3: C2->D->E->F 离线点位
        std::cout << "[线程1] 添加段3: C2->D->E->F" << std::endl;
        std::vector<MotionState<DOF>> waypoints3 = {point_D_, point_E_, point_F_};
        if (!planner_->addTrajectorySegment(waypoints3)) {
            std::cerr << "[线程1] 添加段3失败" << std::endl;
            return;
        }
        
        // 等待段3完成
        {
            std::unique_lock<std::mutex> lock(test_mutex_);
            cv_.wait(lock, [this] { return segment3_completed_.load(); });
        }
        
        std::cout << "[线程1] 所有轨迹段添加完成" << std::endl;
    }
    
    void trajectoryExecutorThread() {
        std::cout << "[线程2] 轨迹执行线程启动" << std::endl;
        
        auto next_cycle = std::chrono::steady_clock::now();
        const auto cycle_duration = std::chrono::microseconds(static_cast<int>(DT * 1e6));
        
        while (is_running_) {
            auto current_time = std::chrono::steady_clock::now();
            double elapsed = std::chrono::duration<double>(current_time - start_time_).count();
            
            // 获取当前目标状态
            TrajectoryState<DOF> target = planner_->getCurrentTarget(elapsed);
            
            if (target.valid) {
                // 记录轨迹数据
                recorded_trajectory_.push_back(target);
                execution_count_++;
                
                // 记录到日志文件
                logTrajectoryState(target);
                // 模拟机器人执行
                simulateRobotExecution(target);
            }
            
            // 检查是否所有段都完成
            if (segment3_completed_.load()) {
                // 再执行1秒确保完成
                if (elapsed > 15.0) {  // 预估总时间
                    is_running_ = false;
                    break;
                }
            }
            
            // 精确定时
            next_cycle += cycle_duration;
            std::this_thread::sleep_until(next_cycle);
        }
        
        std::cout << "[线程2] 轨迹执行线程结束" << std::endl;
    }
    
    void onSegmentCompleted(size_t segment_id) {
        std::cout << ">>> 段 " << segment_id << " 执行完成" << std::endl;
        
        std::lock_guard<std::mutex> lock(test_mutex_);
        if (segment_id == 1) {
            segment1_completed_ = true;
        } else if (segment_id == 2) {
            segment2_completed_ = true;
        } else if (segment_id == 3) {
            segment3_completed_ = true;
        }
        cv_.notify_all();
    }
    
    void logTrajectoryState(const TrajectoryState<DOF>& state) {
        if (log_file_.is_open()) {
            log_file_ << std::fixed << std::setprecision(6)
                      << state.timestamp << ",";
            
            // 确定当前段ID
            size_t current_segment = getCurrentSegmentId();
            log_file_ << current_segment << ",";
            
            // 位置
            for (int i = 0; i < DOF; ++i) {
                log_file_ << state.position[i] << ",";
            }
            
            // 速度
            for (int i = 0; i < DOF; ++i) {
                log_file_ << state.velocity[i];
                if (i < DOF - 1) log_file_ << ",";
            }
            log_file_ << "\n";
        }
    }
    
    void simulateRobotExecution(const TrajectoryState<DOF>& target) {
        // 模拟机器人执行，这里只是定期打印状态
        static size_t print_counter = 0;
        if (++print_counter % 10 == 0) {  // 每500ms打印一次
            std::cout << "[执行] t=" << std::fixed << std::setprecision(3) << target.timestamp
                      << "s, 位置=[" << target.position.head(3).transpose() << "]" << std::endl;
        }
    }
    
    size_t getCurrentSegmentId() {
        // 简单的段ID估算，基于时间
        if (!segment1_completed_) return 1;
        if (!segment2_completed_) return 2;
        if (!segment3_completed_) return 3;
        return 0;
    }
    
    void printTestPlan() {
        std::cout << "\n=== 测试计划 ===" << std::endl;
        std::cout << "段1: A(0,0,0,0,0,0) -> B(1,1,1,1,1,1) -> C(2,2,2,2,2,2) [离线点位]" << std::endl;
        std::cout << "段2: C -> C1 -> C2 [sin曲线, 2秒连续轨迹]" << std::endl;
        std::cout << "段3: C2 -> D -> E(4,4,4,4,4,4) -> F(6,6,6,6,6,6) [离线点位]" << std::endl;
        std::cout << "控制频率: " << CONTROL_FREQ << " Hz" << std::endl;
        std::cout << "线程1: 分次传入轨迹段" << std::endl;
        std::cout << "线程2: 按插补周期轮询获取轨迹" << std::endl;
    }
    
    void analyzeResults() {
        std::cout << "\n=== 测试结果分析 ===" << std::endl;
        std::cout << "总执行次数: " << execution_count_.load() << std::endl;
        std::cout << "记录轨迹点数: " << recorded_trajectory_.size() << std::endl;
        
        if (!recorded_trajectory_.empty()) {
            double total_time = recorded_trajectory_.back().timestamp - recorded_trajectory_.front().timestamp;
            std::cout << "总执行时间: " << std::fixed << std::setprecision(3) << total_time << " 秒" << std::endl;
            std::cout << "平均执行频率: " << std::fixed << std::setprecision(1) 
                      << recorded_trajectory_.size() / total_time << " Hz" << std::endl;
        }
        
        std::cout << "轨迹数据已保存到: hybrid_trajectory_log.csv" << std::endl;
        
        // 验证轨迹连续性
        validateTrajectoryContinuity();
    }
    
    void validateTrajectoryContinuity() {
        std::cout << "\n=== 轨迹连续性验证 ===" << std::endl;
        
        if (recorded_trajectory_.size() < 2) {
            std::cout << "轨迹点不足，无法验证连续性" << std::endl;
            return;
        }
        
        double max_pos_jump = 0.0;
        double max_vel_jump = 0.0;
        
        for (size_t i = 1; i < recorded_trajectory_.size(); ++i) {
            auto& prev = recorded_trajectory_[i-1];
            auto& curr = recorded_trajectory_[i];
            
            double pos_jump = (curr.position - prev.position).norm();
            double vel_jump = (curr.velocity - prev.velocity).norm();
            
            max_pos_jump = std::max(max_pos_jump, pos_jump);
            max_vel_jump = std::max(max_vel_jump, vel_jump);
        }
        
        std::cout << "最大位置跳跃: " << std::scientific << max_pos_jump << std::endl;
        std::cout << "最大速度跳跃: " << std::scientific << max_vel_jump << std::endl;
        
        // 判断连续性
        const double pos_threshold = 0.01;  // 位置跳跃阈值
        const double vel_threshold = 0.1;   // 速度跳跃阈值
        
        bool is_continuous = (max_pos_jump < pos_threshold) && (max_vel_jump < vel_threshold);
        std::cout << "轨迹连续性: " << (is_continuous ? "通过" : "失败") << std::endl;
    }
};

int main() {
    std::cout << "=== 混合轨迹测试程序 ===" << std::endl;
    
    try {
        HybridTrajectoryTest test;
        
        if (!test.initialize()) {
            std::cerr << "测试初始化失败" << std::endl;
            return -1;
        }
        
        test.runTest();
        
        std::cout << "\n测试成功完成！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生错误: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
