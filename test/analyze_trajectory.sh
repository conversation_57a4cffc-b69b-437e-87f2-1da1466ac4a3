#!/bin/bash
# 分析混合轨迹测试结果

CSV_FILE="hybrid_trajectory_log.csv"

if [ ! -f "$CSV_FILE" ]; then
    echo "找不到文件: $CSV_FILE"
    echo "请确保在build目录下运行此脚本"
    exit 1
fi

echo "=== 混合轨迹测试结果分析 ==="
echo

# 基本统计
echo "=== 基本统计 ==="
total_lines=$(wc -l < "$CSV_FILE")
data_lines=$((total_lines - 1))  # 减去标题行
echo "总数据点数: $data_lines"

# 获取时间范围
first_time=$(sed -n '2p' "$CSV_FILE" | cut -d',' -f1)
last_time=$(tail -n1 "$CSV_FILE" | cut -d',' -f1)
echo "时间范围: ${first_time}s - ${last_time}s"

# 计算持续时间
duration=$(echo "$last_time - $first_time" | bc -l)
echo "总持续时间: ${duration}s"

# 计算平均频率
if [ $(echo "$duration > 0" | bc -l) -eq 1 ]; then
    freq=$(echo "scale=1; $data_lines / $duration" | bc -l)
    echo "平均采样频率: ${freq} Hz"
fi

echo

# 分析各段
echo "=== 轨迹段分析 ==="
segments=$(awk -F',' 'NR>1 {print $2}' "$CSV_FILE" | sort -n | uniq)
echo "检测到轨迹段: $segments"

for seg in $segments; do
    echo
    echo "段 $seg:"
    
    # 段的数据点数
    seg_count=$(awk -F',' -v s="$seg" 'NR>1 && $2==s {count++} END {print count+0}' "$CSV_FILE")
    echo "  数据点数: $seg_count"
    
    # 段的时间范围
    seg_times=$(awk -F',' -v s="$seg" 'NR>1 && $2==s {print $1}' "$CSV_FILE")
    if [ -n "$seg_times" ]; then
        seg_start=$(echo "$seg_times" | head -n1)
        seg_end=$(echo "$seg_times" | tail -n1)
        seg_duration=$(echo "$seg_end - $seg_start" | bc -l)
        echo "  时间范围: ${seg_start}s - ${seg_end}s"
        echo "  持续时间: ${seg_duration}s"
        
        # 起始和结束位置
        start_pos=$(awk -F',' -v s="$seg" 'NR>1 && $2==s {print $3","$4","$5","$6","$7","$8; exit}' "$CSV_FILE")
        end_pos=$(awk -F',' -v s="$seg" 'NR>1 && $2==s {pos=$3","$4","$5","$6","$7","$8} END {print pos}' "$CSV_FILE")
        echo "  起始位置: [$start_pos]"
        echo "  结束位置: [$end_pos]"
    fi
done

echo

# 分析sin曲线段（段2）
echo "=== Sin曲线轨迹分析（段2）==="
seg2_data=$(awk -F',' 'NR>1 && $2==2' "$CSV_FILE")

if [ -n "$seg2_data" ]; then
    echo "Sin曲线段数据点数: $(echo "$seg2_data" | wc -l)"
    
    # 分析每个轴的振荡
    for axis in {3..8}; do
        axis_num=$((axis - 2))
        echo "轴$axis_num 分析:"
        
        # 获取位置数据
        positions=$(echo "$seg2_data" | cut -d',' -f$axis)
        
        if [ -n "$positions" ]; then
            # 计算最小值、最大值
            min_pos=$(echo "$positions" | sort -n | head -n1)
            max_pos=$(echo "$positions" | sort -n | tail -n1)
            
            # 计算振幅
            amplitude=$(echo "scale=6; ($max_pos - $min_pos) / 2" | bc -l)
            center=$(echo "scale=6; ($max_pos + $min_pos) / 2" | bc -l)
            
            echo "  中心位置: $center"
            echo "  振幅: $amplitude"
            echo "  范围: [$min_pos, $max_pos]"
        fi
    done
else
    echo "没有找到sin曲线轨迹数据（段2）"
fi

echo

# 验证测试需求
echo "=== 测试需求验证 ==="

# 检查段数
seg_count=$(echo "$segments" | wc -w)
if [ $seg_count -ge 3 ]; then
    echo "✓ 检测到足够的轨迹段 ($seg_count 个)"
else
    echo "✗ 轨迹段数量不足 (检测到 $seg_count 个，期望至少3个)"
fi

# 检查段1起始位置
seg1_start=$(awk -F',' 'NR>1 && $2==1 {print $3","$4","$5","$6","$7","$8; exit}' "$CSV_FILE")
if [ -n "$seg1_start" ]; then
    # 检查是否接近原点
    near_origin=$(echo "$seg1_start" | awk -F',' '{
        sum = 0
        for(i=1; i<=6; i++) {
            if($i > 0.1 || $i < -0.1) sum++
        }
        if(sum == 0) print "yes"; else print "no"
    }')
    
    if [ "$near_origin" = "yes" ]; then
        echo "✓ 段1从原点附近开始"
    else
        echo "✗ 段1起始位置不在原点附近: [$seg1_start]"
    fi
fi

# 检查段1结束位置
seg1_end=$(awk -F',' 'NR>1 && $2==1 {pos=$3","$4","$5","$6","$7","$8} END {print pos}' "$CSV_FILE")
if [ -n "$seg1_end" ]; then
    # 检查是否接近(2,2,2,2,2,2)
    near_target=$(echo "$seg1_end" | awk -F',' '{
        sum = 0
        for(i=1; i<=6; i++) {
            if($i > 2.5 || $i < 1.5) sum++
        }
        if(sum == 0) print "yes"; else print "no"
    }')
    
    if [ "$near_target" = "yes" ]; then
        echo "✓ 段1在目标位置附近结束"
    else
        echo "✗ 段1结束位置不在目标附近: [$seg1_end]"
    fi
fi

# 检查段2持续时间
seg2_duration=$(awk -F',' 'NR>1 && $2==2 {
    if(start=="") start=$1
    end=$1
} END {
    if(start!="" && end!="") print end-start
}' "$CSV_FILE")

if [ -n "$seg2_duration" ]; then
    duration_ok=$(echo "$seg2_duration > 1.0 && $seg2_duration < 3.0" | bc -l)
    if [ $duration_ok -eq 1 ]; then
        echo "✓ 段2持续时间合理: ${seg2_duration}s"
    else
        echo "✗ 段2持续时间不符合预期: ${seg2_duration}s (期望约2秒)"
    fi
fi

# 检查段2是否有振荡
if [ -n "$seg2_data" ]; then
    has_oscillation="no"
    for axis in {3..8}; do
        positions=$(echo "$seg2_data" | cut -d',' -f$axis)
        if [ -n "$positions" ]; then
            min_pos=$(echo "$positions" | sort -n | head -n1)
            max_pos=$(echo "$positions" | sort -n | tail -n1)
            range=$(echo "$max_pos - $min_pos" | bc -l)
            
            # 如果任何轴的变化范围大于0.1，认为有振荡
            if [ $(echo "$range > 0.1" | bc -l) -eq 1 ]; then
                has_oscillation="yes"
                break
            fi
        fi
    done
    
    if [ "$has_oscillation" = "yes" ]; then
        echo "✓ 段2检测到振荡运动"
    else
        echo "✗ 段2未检测到明显振荡"
    fi
fi

# 检查段3结束位置
seg3_end=$(awk -F',' 'NR>1 && $2==3 {pos=$3","$4","$5","$6","$7","$8} END {print pos}' "$CSV_FILE")
if [ -n "$seg3_end" ]; then
    # 检查是否接近(6,6,6,6,6,6)
    near_final=$(echo "$seg3_end" | awk -F',' '{
        sum = 0
        for(i=1; i<=6; i++) {
            if($i > 7.0 || $i < 5.0) sum++
        }
        if(sum == 0) print "yes"; else print "no"
    }')
    
    if [ "$near_final" = "yes" ]; then
        echo "✓ 段3在最终目标位置附近结束"
    else
        echo "✗ 段3结束位置不在最终目标附近: [$seg3_end]"
    fi
fi

echo
echo "=== 分析完成 ==="
echo "详细数据已保存在: $CSV_FILE"
