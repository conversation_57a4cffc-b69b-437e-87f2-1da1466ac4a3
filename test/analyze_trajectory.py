#!/usr/bin/env python3
"""
分析混合轨迹测试结果（无图形界面版本）
"""

import pandas as pd
import numpy as np
import sys
import os

def load_trajectory_data(csv_file):
    """加载轨迹数据"""
    try:
        data = pd.read_csv(csv_file)
        return data
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def analyze_trajectory_continuity(data):
    """分析轨迹连续性"""
    print("\n=== 轨迹连续性分析 ===")
    
    # 计算位置跳跃
    pos_cols = [f'pos_{i}' for i in range(1, 7)]
    vel_cols = [f'vel_{i}' for i in range(1, 7)]
    
    pos_diff = data[pos_cols].diff().abs()
    vel_diff = data[vel_cols].diff().abs()
    
    max_pos_jump = pos_diff.max().max()
    max_vel_jump = vel_diff.max().max()
    
    print(f"最大位置跳跃: {max_pos_jump:.6f}")
    print(f"最大速度跳跃: {max_vel_jump:.6f}")
    
    # 分析段间连接
    segments = sorted(data['segment_id'].unique())
    print(f"\n检测到轨迹段: {segments}")
    
    for i in range(len(segments)-1):
        seg1_data = data[data['segment_id'] == segments[i]]
        seg2_data = data[data['segment_id'] == segments[i+1]]
        
        if len(seg1_data) > 0 and len(seg2_data) > 0:
            seg1_end = seg1_data.iloc[-1]
            seg2_start = seg2_data.iloc[0]
            
            pos_jump = np.sqrt(sum([(seg2_start[f'pos_{j}'] - seg1_end[f'pos_{j}'])**2 for j in range(1, 7)]))
            vel_jump = np.sqrt(sum([(seg2_start[f'vel_{j}'] - seg1_end[f'vel_{j}'])**2 for j in range(1, 7)]))
            
            print(f"段{segments[i]}->段{segments[i+1]} 位置跳跃: {pos_jump:.6f}, 速度跳跃: {vel_jump:.6f}")

def analyze_sin_trajectory(data):
    """分析sin曲线轨迹"""
    print("\n=== Sin曲线轨迹分析 ===")
    
    segment2_data = data[data['segment_id'] == 2]
    
    if segment2_data.empty:
        print("没有找到sin曲线轨迹数据")
        return
    
    print(f"Sin曲线段数据点数: {len(segment2_data)}")
    print(f"Sin曲线段持续时间: {segment2_data['timestamp'].max() - segment2_data['timestamp'].min():.3f} 秒")
    
    # 分析每个轴的振荡
    for i in range(1, 7):
        pos_col = f'pos_{i}'
        positions = segment2_data[pos_col].values
        
        if len(positions) > 0:
            mean_pos = np.mean(positions)
            amplitude = (np.max(positions) - np.min(positions)) / 2
            print(f"轴{i}: 中心位置={mean_pos:.3f}, 振幅={amplitude:.3f}, 范围=[{np.min(positions):.3f}, {np.max(positions):.3f}]")

def print_statistics(data):
    """打印统计信息"""
    print("\n=== 轨迹统计信息 ===")
    print(f"总数据点数: {len(data)}")
    
    if len(data) > 1:
        total_time = data['timestamp'].max() - data['timestamp'].min()
        print(f"总执行时间: {total_time:.3f} 秒")
        print(f"平均采样频率: {len(data) / total_time:.1f} Hz")
    
    segments = sorted(data['segment_id'].unique())
    for seg in segments:
        seg_data = data[data['segment_id'] == seg]
        if len(seg_data) > 1:
            duration = seg_data['timestamp'].max() - seg_data['timestamp'].min()
            print(f"段{seg}: {len(seg_data)} 点, 持续时间: {duration:.3f} 秒")
        else:
            print(f"段{seg}: {len(seg_data)} 点")

def analyze_trajectory_phases(data):
    """分析轨迹各阶段"""
    print("\n=== 轨迹阶段分析 ===")
    
    segments = sorted(data['segment_id'].unique())
    
    for seg in segments:
        seg_data = data[data['segment_id'] == seg]
        if len(seg_data) == 0:
            continue
            
        print(f"\n段{seg}:")
        
        # 起始和结束位置
        start_pos = [seg_data.iloc[0][f'pos_{i}'] for i in range(1, 7)]
        end_pos = [seg_data.iloc[-1][f'pos_{i}'] for i in range(1, 7)]
        
        print(f"  起始位置: [{', '.join([f'{p:.3f}' for p in start_pos])}]")
        print(f"  结束位置: [{', '.join([f'{p:.3f}' for p in end_pos])}]")
        
        # 最大速度
        max_vels = [seg_data[f'vel_{i}'].abs().max() for i in range(1, 7)]
        print(f"  最大速度: [{', '.join([f'{v:.3f}' for v in max_vels])}]")
        
        # 位移距离
        displacement = np.sqrt(sum([(end_pos[i] - start_pos[i])**2 for i in range(6)]))
        print(f"  总位移: {displacement:.3f}")

def validate_test_requirements(data):
    """验证测试需求是否满足"""
    print("\n=== 测试需求验证 ===")
    
    segments = sorted(data['segment_id'].unique())
    
    # 检查是否有3个段
    if len(segments) >= 3:
        print("✓ 检测到3个轨迹段")
    else:
        print(f"✗ 只检测到{len(segments)}个轨迹段，期望3个")
    
    # 检查段1: A->B->C
    seg1_data = data[data['segment_id'] == 1]
    if not seg1_data.empty:
        start_pos = [seg1_data.iloc[0][f'pos_{i}'] for i in range(1, 7)]
        end_pos = [seg1_data.iloc[-1][f'pos_{i}'] for i in range(1, 7)]
        
        # 检查是否从(0,0,0,0,0,0)开始
        if all(abs(p) < 0.1 for p in start_pos):
            print("✓ 段1从原点开始")
        else:
            print(f"✗ 段1起始位置不是原点: {start_pos}")
        
        # 检查是否接近(2,2,2,2,2,2)结束
        if all(abs(p - 2.0) < 0.5 for p in end_pos):
            print("✓ 段1在接近(2,2,2,2,2,2)处结束")
        else:
            print(f"✗ 段1结束位置不符合预期: {end_pos}")
    
    # 检查段2: sin曲线
    seg2_data = data[data['segment_id'] == 2]
    if not seg2_data.empty:
        duration = seg2_data['timestamp'].max() - seg2_data['timestamp'].min()
        if 1.5 < duration < 2.5:
            print(f"✓ 段2持续时间约2秒: {duration:.3f}s")
        else:
            print(f"✗ 段2持续时间不符合预期: {duration:.3f}s")
        
        # 检查是否有振荡
        has_oscillation = False
        for i in range(1, 7):
            pos_range = seg2_data[f'pos_{i}'].max() - seg2_data[f'pos_{i}'].min()
            if pos_range > 0.1:
                has_oscillation = True
                break
        
        if has_oscillation:
            print("✓ 段2检测到振荡运动")
        else:
            print("✗ 段2未检测到明显振荡")
    
    # 检查段3: C2->D->E->F
    seg3_data = data[data['segment_id'] == 3]
    if not seg3_data.empty:
        end_pos = [seg3_data.iloc[-1][f'pos_{i}'] for i in range(1, 7)]
        if all(abs(p - 6.0) < 1.0 for p in end_pos):
            print("✓ 段3在接近(6,6,6,6,6,6)处结束")
        else:
            print(f"✗ 段3结束位置不符合预期: {end_pos}")

def main():
    # 检查CSV文件
    csv_file = 'hybrid_trajectory_log.csv'
    if not os.path.exists(csv_file):
        print(f"找不到文件: {csv_file}")
        print("请确保在build目录下运行此脚本")
        return
    
    # 加载数据
    data = load_trajectory_data(csv_file)
    if data is None:
        return
    
    # 执行各种分析
    print_statistics(data)
    analyze_trajectory_phases(data)
    analyze_sin_trajectory(data)
    analyze_trajectory_continuity(data)
    validate_test_requirements(data)
    
    print("\n=== 分析完成 ===")

if __name__ == "__main__":
    main()
