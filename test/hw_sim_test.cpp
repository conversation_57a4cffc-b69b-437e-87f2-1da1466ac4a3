#include "comm/HWComm.h"
#include <iostream>
#include <signal.h>
#include <atomic>
#include <thread>
#include <chrono>
#include <iomanip>

// std::atomic<bool> g_stop_flag(false);

// void signal_handler(int sig) {
//     std::cout << "\nInterrupt signal (" << sig << ") received." << std::endl;
//     g_stop_flag = true;
// }

int main() {
    std::cout << "=== Hardware Simulation ===" << std::endl;
    
    // // 设置信号处理
    // signal(SIGINT, signal_handler);
    // signal(SIGTERM, signal_handler);
    
    // 创建硬件模拟实例 (默认使用 ZMQ)
    HWComm hw(CommType::ZMQ);//ZMQ
    
    if (!hw.init()) {
        std::cerr << "Failed to initialize hardware simulation" << std::endl;
        return -1;
    }
    
    std::cout << "Communication type: " << (hw.get_connection_info().find("ZMQ") != std::string::npos ? "ZMQ" : "FIFO") << std::endl;
    std::cout << "Connection: " << hw.get_connection_info() << std::endl;
    
    // 设置实时数据接收回调
    hw.recvRtData([](const ServoCommand& cmd) {
        static uint64_t last_timestamp_us = 0;
        static int recv_count = 0;
        static auto start_time = std::chrono::steady_clock::now();

        recv_count++;
        auto current_time = std::chrono::steady_clock::now();

        // 计算接收间隔
        double interval_ms = 0.0;
        if (last_timestamp_us > 0) {
            interval_ms = (cmd.timestamp_us - last_timestamp_us) / 1000.0;
        }

        // 计算接收频率
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - start_time);
        double avg_freq = (elapsed.count() > 0) ? (recv_count * 1000.0 / elapsed.count()) : 0.0;

        std::cout << "Recv: [" << recv_count << "]: "
                  << " duration=" << cmd.duration_ms << "ms"
                  << " interval=" << std::fixed << std::setprecision(2) << interval_ms << "ms"
                  << " freq=" << std::setprecision(1) << avg_freq << "Hz" << std::endl;

        last_timestamp_us = cmd.timestamp_us;
    });
    
    // 反馈通道演示
    std::cout << "\n[HW] Sending feedback to robot..." << std::endl;
    bool fb_sent = hw.sendFeedback("Test feedback from HW");
    std::cout << "[HW] Feedback sent: " << (fb_sent ? "OK" : "Failed") << std::endl;

    std::cout << "[HW] Receiving feedback from robot..." << std::endl;
    for (int i = 0; i < 50 ; ++i) {
        hw.recvFeedback([](const std::string& feedback) {
            std::cout << "[HW] Received feedback: " << feedback << std::endl;
        });
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    hw.run();

    std::cout << "Hardware simulation stopped." << std::endl;
    return 0;
} 